import React, { useState,useEffect } from 'react';
import { View, Text, StyleSheet, SafeAreaView, ScrollView, TouchableOpacity } from 'react-native';
import CalendarStrip from 'react-native-calendar-strip';
import { useNavigation } from '@react-navigation/native';
import IndexStyle from '../../Theme/IndexStyle';
import { PrimaryColors } from '../../Utils/Constants';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import CurvHeader from '../../CommonComponents/CurvHeader';
import ErpApi from '../../config/ErpApi';
import moment from 'moment';

const Homework = () => {
  const navigation = useNavigation();
  const { isDarkMode } = IndexStyle();
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  // const [classworkData, setClassworkdata] = useState([]);

  const getDateDisplayText = (selectedDate) => {
    const today = new Date();
    const selected = new Date(selectedDate);
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    const isSameDay = (date1, date2) =>
      date1.getDate() === date2.getDate() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getFullYear() === date2.getFullYear();

    if (isSameDay(selected, today)) {
      return 'Today';
    } else if (isSameDay(selected, yesterday)) {
      return 'Yesterday';
    } else if (isSameDay(selected, tomorrow)) {
      return 'Tomorrow';
    } else {
      const day = selected.getDate();
      const month = selected.toLocaleString('default', { month: 'short' });
      let suffix = 'th';
      if (day % 10 === 1 && day !== 11) suffix = 'st';
      else if (day % 10 === 2 && day !== 12) suffix = 'nd';
      else if (day % 10 === 3 && day !== 13) suffix = 'rd';
      return `${day}${suffix} ${month}`;
    }
  };

  useEffect(() => {
    // getHomeWork();
  }, []);
  const classworkData = [
    {
      subject: 'Mathematics',
      classwork: 'Algebra Exercises',
      date: '2025-06-16',
      displayDate: '16 June 2025 Monday',
    },
    {
      subject: 'English',
      classwork: 'Essay Writing',
      date: '2025-06-16',
      displayDate: '16 June 2025 Monday',
    },
    {
      subject: 'Sanskrit',
      classwork: 'Grammar Practice',
      date: '2025-06-16',
      displayDate: '16 June 2025 Monday',
    },
    {
      subject: 'Hindi',
      classwork: 'Poetry Analysis',
      date: '2025-06-17',
      displayDate: '17 June 2025 Tuesday',
    },
    {
      subject: 'English',
      classwork: 'Essay Writing',
      date: '2025-06-17',
      displayDate: '17 June 2025 Tuesday',
    },
    {
      subject: 'Sanskrit',
      classwork: 'Grammar Practice',
      date: '2025-06-18',
      displayDate: '18 June 2025 Wednesday',
    },
    {
      subject: 'Hindi',
      classwork: 'Poetry Analysis',
      date: '2025-06-18',
      displayDate: '18 June 2025 Wednesday',
    },
  ];
  const getHomeWork = async () => {
    try {
      const res = await ErpApi.HomeworkDetails.homework();
      const text = await res.text(); // Get raw response
      console.log('Raw classWork response:', text);
  
      if (!res.ok) {
        console.error(`HTTP Error: ${res.status}`);
        return;
      }
  
      const json = JSON.parse(text); // manually parse after logging
      if (json?.data) {
        setClassworkdata(json.data);
      } else {
        console.warn('No data in classWork response:', json);
        setClassworkdata([]);
      }
    } catch (err) {
      console.error('Failed to fetch classWork:', err);
      setClassworkdata([]);
    }
  };

  const filteredClassworkData = classworkData.filter(
    (entry) => entry.date === selectedDate
  );

  return (
    <SafeAreaProvider>
      <SafeAreaView
        style={{flex: 1,backgroundColor: isDarkMode ? '#161616' : PrimaryColors.WHITE}}
        edges={['left', 'right']}
      >
        <CurvHeader
          title="Home Work"
          isBack={true}
          onBackPress={() => {
            navigation.goBack();
          }}
        />
        <View style={styles.calendarContainer}>
          <CalendarStrip
            scrollable={true}
            style={[styles.calendarStrip,{backgroundColor:isDarkMode?'#161616':PrimaryColors.WHITE}]}
            calendarHeaderStyle={{ display: 'none' }}
            dateNumberStyle={[styles.dateNumber,{color:isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK}]}
            dateNameStyle={[styles.dateName,{color:isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK}]}
            highlightDateNumberStyle={styles.highlightDateNumber}
            highlightDateNameStyle={styles.highlightDateName}
            disabledDateNameStyle={styles.disabledDateName}
            disabledDateNumberStyle={styles.disabledDateNumber}
            selectedDate={moment(selectedDate)}
            onDateSelected={(date) => {
              setSelectedDate(date.format('YYYY-MM-DD'));
            }}
            minDate={moment('2020-01-01')}
            maxDate={moment('2030-12-31')}
            calendarColor={'#fff'}
            leftSelector={[]}
            rightSelector={[]}
            dayContainerStyle={styles.dayContainer}
          />
        </View>

        <View style={styles.dateDisplayContainer}>
          <Text style={[styles.dateDisplayText,{color:isDarkMode?PrimaryColors.WHITE:PrimaryColors.BLACK}]}>{getDateDisplayText(selectedDate)}</Text>
        </View>

        <ScrollView
          style={styles.timetableScrollView}
          contentContainerStyle={styles.timetableContentContainer}
        >
          {filteredClassworkData.length > 0 ? (
            filteredClassworkData.map((entry, index) => (
              <TouchableOpacity key={index} style={[styles.timetableCard,{backgroundColor:isDarkMode?PrimaryColors.BLACK:PrimaryColors.WHITE,borderColor: isDarkMode ? "#444" : '#CCCCCC'}]}>
                <View style={[styles.tagContainer,{backgroundColor:isDarkMode?'#161616':PrimaryColors.BLACK}]}>
                  <Text style={styles.tagText}>{entry.subject}</Text>
                </View>
                <Text style={[styles.classworkText,{color:isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK}]}>
                  Classwork: <Text style={styles.classworkDetail}>{entry.classwork}</Text>
                </Text>
                <Text style={styles.dateText}>{entry.displayDate}</Text>
              </TouchableOpacity>
            ))
          ) : (
            <Text style={styles.noDataText}>No classwork for this date.</Text>
          )}
        </ScrollView>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: PrimaryColors.WHITE,
  },
  calendarContainer: {
    marginHorizontal: 10,
    marginVertical: 10,
    borderRadius: 10,
    backgroundColor: '#fff',
    borderColor: '#ccc',
  },
  calendarStrip: {
    height: 90,
    paddingTop: 10,
    paddingBottom: 10,
  },
  dayContainer: {
    width: 40,
    // height: 70,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 2,
  },
  dateNumber: {
    fontSize: 16,
    color: '#000',
    fontWeight: 'bold',
  },
  dateName: {
    fontSize: 12,
    color: '#000',
    marginTop: 2,
  },
  highlightDateNumber: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: 'bold',
    backgroundColor: '#FF7825',
    borderRadius: 16,
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  highlightDateName: {
    fontSize: 12,
    color: '#FF7825',
    marginTop: 2,
  },
  disabledDateName: {
    fontSize: 12,
    color: '#d9e1e8',
    marginTop: 2,
  },
  disabledDateNumber: {
    fontSize: 16,
    color: '#d9e1e8',
  },
  dateDisplayContainer: {
    alignItems: 'flex-start',
    marginTop: 10,
    marginLeft: 20,
  },
  dateDisplayText: {
    fontSize: 20,
    fontWeight: '700',
    color: '#000',
  },
  timetableScrollView: {
    flex: 1,
    marginHorizontal: 20,
    marginTop: 16,
  },
  timetableContentContainer: {
    paddingBottom: 20,
  },
  timetableCard: {
    backgroundColor: '#fff',
    borderRadius: 15,
    marginBottom: 15,
    padding: 15,
    borderColor: '#E5D7D7',
    borderWidth: 1,
  },
  tagContainer: {
    backgroundColor: '#000',
    borderRadius: 20,
    paddingVertical: 5,
    paddingHorizontal: 10,
    alignSelf: 'flex-start',
    marginBottom: 10,
  },
  tagText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  classworkText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#000',
    marginBottom: 5,
  },
  classworkDetail: {
    fontWeight: '500',
  },
  dateText: {
    fontSize: 12,
    fontWeight: '400',
    color: '#777',
  },
  noDataText: {
    fontSize: 16,
    color: '#777',
    textAlign: 'center',
    marginTop: 20,
  },
});

export default Homework;
