import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Animated, Dimensions } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ERPStackParamList } from '../../route/ERPStack';
import { PrimaryColors } from '../../Utils/Constants';

type Props = NativeStackScreenProps<ERPStackParamList, 'SplashERP'>;
const { width, height } = Dimensions.get('window');

const SplashERP: React.FC<Props> = ({ navigation }) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.5)).current;
  const textBounceAnim = useRef(new Animated.Value(0)).current;
  const backgroundAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.sequence([
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1.2,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.spring(textBounceAnim, {
        toValue: 1,
        friction: 3,
        tension: 40,
        useNativeDriver: true,
      }),
      Animated.delay(500),
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(backgroundAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: false,
      }),
    ]).start(() => {
      navigation.replace('Home');
    });
  }, [fadeAnim, scaleAnim, textBounceAnim, backgroundAnim, navigation]);

  const backgroundColor = backgroundAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [PrimaryColors.BLACK, '#000000'],
  });

  return (
    <Animated.View style={[styles.container, { backgroundColor }]}>
      <Animated.View
        style={{
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
          alignItems: 'center',
        }}
      >
        <View style={styles.centerSquare}>
          <Animated.View
            style={{
              transform: [
                {
                  scale: textBounceAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0.5, 1],
                  }),
                },
              ],
            }}
          >
            <Text style={styles.welcomeText}>Welcome to</Text>
            <View style={styles.logoContainer}>
              <Text style={styles.logoText}>Uest Classes</Text>
              <View style={styles.lineAbove} />
              <View style={styles.lineBelow} />
            </View>
          </Animated.View>
        </View>
      </Animated.View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  welcomeText: {
    fontSize: 30,
    color: '#FF8C00',
    textAlign: 'center',
  },
  logoContainer: {
    position: 'relative',
    marginTop: 10,
  },
  logoText: {
    color: '#fff',
    fontSize: 40,
    fontWeight: 'bold',
    letterSpacing: 2,
    textAlign: 'center',
  },
  lineAbove: {
    position: 'absolute',
    top: -5,
    width: '60%',
    height: 2,
    backgroundColor: '#FF8C00',
    alignSelf: 'center',
  },
  lineBelow: {
    position: 'absolute',
    bottom: -5,
    width: '60%',
    height: 2,
    backgroundColor: '#FF8C00',
    alignSelf: 'center',
  },
  centerSquare: {
    width: width * 1.2,
    height: width * 0.8,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: '100%',
    borderTopRightRadius: '100%',
    borderBottomLeftRadius: '100%',
    borderBottomRightRadius: '100%',
  },
});

export default SplashERP;
