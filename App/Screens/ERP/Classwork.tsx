import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, SafeAreaView, ScrollView, TouchableOpacity } from 'react-native';
import CalendarStrip from 'react-native-calendar-strip';
import { useNavigation } from '@react-navigation/native';
import IndexStyle from '../../Theme/IndexStyle';
import { PrimaryColors } from '../../Utils/Constants';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import CurvHeader from '../../CommonComponents/CurvHeader';
import ErpApi from '../../config/ErpApi';
import moment from 'moment';

const Classwork = () => {
  const navigation = useNavigation();
  const { isDarkMode } = IndexStyle();
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [classworkdata, setClassworkdata] = useState([]);

  useEffect(() => {
    getClassWork();
  }, []);

  // const getClassWork = () => {
  //   var response = {
  //     "draw": 1,
  //     "recordsTotal": 1,
  //     "recordsFiltered": 1,
  //     "data": [
  //         {
  //             "id": 13,
  //             "class_uuid": "1",
  //             "classroom_id": "Std 11th",
  //             "subject_id": "Maths",
  //             "classwork_date": "2025-06-17",
  //             "title": "HIII",
  //             "description": "<p><strong>jhjshjdsjhdjshhshd  <\/strong><i><strong><u>sdsds<\/u><\/strong><\/i><\/p>",
  //             "created_at": "2025-06-17T09:19:09.000000Z",
  //             "updated_at": "2025-06-17T09:21:39.000000Z",
  //             "classroom": {
  //                 "id": 1,
  //                 "class_name": "Std 11th",
  //                 "department_id": 1,
  //                 "class_teacher_id": 3,
  //                 "year_id": 1,
  //                 "created_at": "2025-06-17T03:34:14.000000Z",
  //                 "updated_at": "2025-06-17T03:34:14.000000Z"
  //             },
  //             "subject": {
  //                 "id": 1,
  //                 "subject_name": "Maths",
  //                 "classroom_id": 1,
  //                 "year_id": 1,
  //                 "created_at": "2025-06-17T03:34:27.000000Z",
  //                 "updated_at": "2025-06-17T03:34:27.000000Z"
  //             },
  //             "": null,
  //             "action": "<button data-toggle=\"modal\"\r\n                                    data-target=\"#newClassWorkEntry\"\r\n                                    data-editClassWorkid=\"13\"\r\n                                    class=\"btn editClassWorkEntry\">\r\n                                    <i class=\"fas fa-edit\"><\/i>\r\n                                <\/button><button type=\"button\"\r\n                    class=\"deleteClassWorkEntry btn\" title=\"Delete\"\r\n                    data-classWorkid=\"13\"><i class=\"fa fa-trash\"><\/i><\/button>"
  //         }
  //     ],
  //     "disableOrdering": false,
  //     "queries": [
  //         {
  //             "query": "select count(*) as aggregate from (select * from classworks order by classworks.id desc) count_row_table",
  //             "bindings": [],
  //             "time": 1.74
  //         },
  //         {
  //             "query": "select * from classworks order by classworks.id desc limit 10 offset 0",
  //             "bindings": [],
  //             "time": 0.52
  //         },
  //         {
  //             "query": "select * from classrooms where classrooms.id in (1)",
  //             "bindings": [],
  //             "time": 1.54
  //         },
  //         {
  //             "query": "select * from subjects where subjects.id in (1)",
  //             "bindings": [],
  //             "time": 1.47
  //         }
  //     ],
  //     "input": {
  //         "draw": "1",
  //         "columns": [
  //             {
  //                 "data": "action",
  //                 "name": "action",
  //                 "searchable": "true",
  //                 "orderable": "false",
  //                 "search": {
  //                     "value": null,
  //                     "regex": "false"
  //                 }
  //             },
  //             {
  //                 "data": "classwork_date",
  //                 "name": "classwork_date",
  //                 "searchable": "true",
  //                 "orderable": "true",
  //                 "search": {
  //                     "value": null,
  //                     "regex": "false"
  //                 }
  //             },
  //             {
  //                 "data": "classroom_id",
  //                 "name": "classroom_id",
  //                 "searchable": "true",
  //                 "orderable": "true",
  //                 "search": {
  //                     "value": null,
  //                     "regex": "false"
  //                 }
  //             },
  //             {
  //                 "data": "subject_id",
  //                 "name": "subject_id",
  //                 "searchable": "true",
  //                 "orderable": "true",
  //                 "search": {
  //                     "value": null,
  //                     "regex": "false"
  //                 }
  //             },
  //             {
  //                 "data": "title",
  //                 "name": "title",
  //                 "searchable": "true",
  //                 "orderable": "true",
  //                 "search": {
  //                     "value": null,
  //                     "regex": "false"
  //                 }
  //             },
  //             {
  //                 "data": "description",
  //                 "name": "description",
  //                 "searchable": "true",
  //                 "orderable": "true",
  //                 "search": {
  //                     "value": null,
  //                     "regex": "false"
  //                 }
  //             }
  //         ],
  //         "start": "0",
  //         "length": "10",
  //         "search": {
  //             "value": null,
  //             "regex": "false"
  //         },
  //         "_": "1750172856605"
  //     }
  // }
  //   setClassworkdata(response.data);
  // };

  const getClassWork = async () => {
    try {
      const res = await ErpApi.ClassworkDetails.classWork();
  
      const text = await res.text(); // Get raw response
      console.log('Raw classWork response:', text);
  
      if (!res.ok) {
        console.error(`HTTP Error: ${res.status}`);
        return;
      }
  
      const json = JSON.parse(text); // manually parse after logging
      if (json?.data) {
        setClassworkdata(json.data);
      } else {
        console.warn('No data in classWork response:', json);
        setClassworkdata([]);
      }
    } catch (err) {
      console.error('Failed to fetch classWork:', err);
      setClassworkdata([]);
    }
  };
  

  // Helper function to get display text for the selected date
  const getDateDisplayText = (selectedDate) => {
    const today = new Date();
    const selected = new Date(selectedDate);
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    const isSameDay = (date1, date2) =>
      date1.getDate() === date2.getDate() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getFullYear() === date2.getFullYear();

    if (isSameDay(selected, today)) {
      return 'Today';
    } else if (isSameDay(selected, yesterday)) {
      return 'Yesterday';
    } else if (isSameDay(selected, tomorrow)) {
      return 'Tomorrow';
    } else {
      const day = selected.getDate();
      const month = selected.toLocaleString('default', { month: 'short' });
      let suffix = 'th';
      if (day % 10 === 1 && day !== 11) suffix = 'st';
      else if (day % 10 === 2 && day !== 12) suffix = 'nd';
      else if (day % 10 === 3 && day !== 13) suffix = 'rd';
      return `${day}${suffix} ${month}`;
    }
  };

  // Helper function to format classwork_date into displayDate (e.g., "3rd Jun")
  const formatDisplayDate = (classworkDate) => {
    const date = new Date(classworkDate);
    const day = date.getDate();
    const month = date.toLocaleString('default', { month: 'short' });
    const weekday = date.toLocaleString('default', { weekday: 'long' });
    const year = date.getFullYear();
    let suffix = 'th';
    if (day % 10 === 1 && day !== 11) suffix = 'st';
    else if (day % 10 === 2 && day !== 12) suffix = 'nd';
    else if (day % 10 === 3 && day !== 13) suffix = 'rd';
    return `${day}${suffix} ${month} ${year} ${weekday}`;
  };

  // Helper function to strip HTML tags from description
  const stripHtmlTags = (html) => {
    return html.replace(/<[^>]+>/g, '').trim();
  };

  // Filter classwork data based on the selected date
  const filteredClassworkData = classworkdata.filter(
    (entry) => entry.classwork_date === selectedDate
  );

  return (
    <SafeAreaProvider>
      <SafeAreaView
        style={{
          flex: 1,
          backgroundColor: isDarkMode ? '#161616' : PrimaryColors.WHITE,
        }}
        edges={['left', 'right']}
      >
        <CurvHeader
          title="Classwork"
          isBack={true}
          onBackPress={() => {
            navigation.goBack();
          }}
        />

        <View style={[styles.calendarContainer]}>
          <CalendarStrip
            scrollable={true}
            style={[styles.calendarStrip, { backgroundColor: isDarkMode ? '#161616' : PrimaryColors.WHITE }]}
            calendarHeaderStyle={{ display: 'none' }}
            dateNumberStyle={[styles.dateNumber, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}
            dateNameStyle={[styles.dateName, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}
            highlightDateNumberStyle={styles.highlightDateNumber}
            highlightDateNameStyle={styles.highlightDateName}
            disabledDateNameStyle={styles.disabledDateName}
            disabledDateNumberStyle={styles.disabledDateNumber}
            selectedDate={moment(selectedDate)}
            onDateSelected={(date) => {
              setSelectedDate(date.format('YYYY-MM-DD'));
            }}
            minDate={moment('2020-01-01')}
            maxDate={moment('2030-12-31')}
            leftSelector={[]}
            rightSelector={[]}
            dayContainerStyle={styles.dayContainer}
          />
        </View>

        <View style={styles.dateDisplayContainer}>
          <Text style={[styles.dateDisplayText, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
            {getDateDisplayText(selectedDate)}
          </Text>
        </View>

        <ScrollView
          style={styles.timetableScrollView}
          contentContainerStyle={styles.timetableContentContainer}
        >
          {filteredClassworkData.length > 0 ? (
            filteredClassworkData.map((entry, index) => (
              <TouchableOpacity
                key={index}
                style={[styles.timetableCard, { backgroundColor: isDarkMode ? PrimaryColors.BLACK : PrimaryColors.WHITE, borderColor: isDarkMode ? '#161616' : '#C7C7C7' }]}
              >
                <View style={[styles.tagContainer, { backgroundColor: isDarkMode ? '#161616' : PrimaryColors.BLACK }]}>
                  <Text style={styles.tagText}>{entry.subject.subject_name}</Text>
                </View>
                <Text style={[styles.classworkText, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>
                  Classwork: <Text style={styles.classworkDetail}>{entry.title}</Text>
                </Text>
                <Text style={styles.dateText}>{formatDisplayDate(entry.classwork_date)}</Text>
              </TouchableOpacity>
            ))
          ) : (
            <Text style={styles.noDataText}>No classwork for this date.</Text>
          )}
        </ScrollView>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  calendarContainer: {
    marginHorizontal: 10,
    marginVertical: 10,
    borderRadius: 10,
  },
  calendarStrip: {
    height: 90,
    paddingTop: 10,
    paddingBottom: 10,
  },
  dayContainer: {
    width: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 2,
  },
  dateNumber: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  dateName: {
    fontSize: 12,
    marginTop: 2,
  },
  highlightDateNumber: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: 'bold',
    backgroundColor: '#FF7825',
    borderRadius: 16,
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  highlightDateName: {
    fontSize: 12,
    color: '#FF7825',
    marginTop: 2,
  },
  disabledDateName: {
    fontSize: 12,
    color: '#d9e1e8',
    marginTop: 2,
  },
  disabledDateNumber: {
    fontSize: 16,
    color: '#d9e1e8',
  },
  dateDisplayContainer: {
    alignItems: 'flex-start',
    marginTop: 10,
    marginLeft: 20,
  },
  dateDisplayText: {
    fontSize: 20,
    fontWeight: '700',
  },
  timetableScrollView: {
    flex: 1,
    marginHorizontal: 20,
    marginTop: 16,
  },
  timetableContentContainer: {
    paddingBottom: 20,
  },
  timetableCard: {
    borderRadius: 15,
    marginBottom: 15,
    padding: 15,
    borderWidth: 1,
  },
  tagContainer: {
    borderRadius: 20,
    paddingVertical: 5,
    paddingHorizontal: 10,
    alignSelf: 'flex-start',
    marginBottom: 10,
  },
  tagText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  classworkText: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 5,
  },
  classworkDetail: {
    fontWeight: '500',
  },
  dateText: {
    fontSize: 12,
    fontWeight: '400',
    color: '#777',
  },
  noDataText: {
    fontSize: 16,
    color: '#777',
    textAlign: 'center',
    marginTop: 20,
  },
});

export default Classwork;