import React from 'react';
import { View, StyleSheet, TouchableOpacity, Text, Platform, Animated } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useSelector } from 'react-redux';
import Dashboard from '../Screens/ERP/Dashboard';
import Profile from '../Screens/ERP/Profile';
import { PrimaryColors } from '../Utils/Constants';
import Home from '../Screens/Student/Home/Home';
// Placeholder for strings
const strings = {
  Home: {
    BACK: 'Back',
    DASHBOARD: 'Dashboard',
    PROFILE: 'Profile',
  },
};

// Tab Items Array
const tabItems = [
  { name: strings.Home.BACK, icon: 'arrow-back' },
  { name: strings.Home.DASHBOARD, component: Dashboard, icon: 'home' },
  { name: strings.Home.PROFILE, component: Profile, icon: 'person' },
];

// Create Bottom Tab Navigator
const Tab = createBottomTabNavigator();

// Custom Tab Bar Component
const CustomTabBar = ({ state, descriptors, navigation }) => {
  const isDarkMode = useSelector((state) => state.theme.isDarkMode);
  const styles = isDarkMode ? darkStyles : lightStyles;

  // Animation for active tab indicator
  const animatedValues = state.routes.map(() => new Animated.Value(0));

  React.useEffect(() => {
    state.routes.forEach((_, index) => {
      Animated.timing(animatedValues[index], {
        toValue: state.index === index ? 1 : 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    });
  }, [state.index]);

  return (
    <View style={styles.tabBarContainer}>
      {/* Drag Handle */}
   
      {/* Tab Bar */}
      <View style={styles.tabBar}>
        {state.routes.map((route, index) => {
          const { options } = descriptors[route.key];
          const label = route.name;
          const isFocused = state.index === index;

          // Find the icon for the current tab
          const tabItem = tabItems.find((item) => item.name === label);
          const iconName = tabItem?.icon || 'circle';

          // Handle tab press
          const onPress = () => {
            if (label === strings.Home.BACK) {
              navigation.goBack();
            } else {
              const event = navigation.emit({
                type: 'tabPress',
                target: route.key,
                canPreventDefault: true,
              });

              if (!isFocused && !event.defaultPrevented) {
                navigation.navigate(route.name);
              }
            }
          };

          // Animated style for indicator
          const indicatorStyle = {
            opacity: animatedValues[index],
            transform: [
              {
                scale: animatedValues[index].interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, 1],
                }),
              },
            ],
          };

          return (
            <TouchableOpacity
              key={index}
              onPress={onPress}
              style={styles.tabItem}
              accessibilityLabel={label}
              accessibilityRole="button"
            >
              <View style={styles.tabContent}>
                <Ionicons
                  name={isFocused ? iconName : `${iconName}-outline`}
                  size={26}
                  color={isFocused ? (isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK) : '#9CA3AF'}
                />
                <Text
                  style={[
                    styles.tabLabel,
                    { color: isFocused ? (isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK) : '#9CA3AF' },
                  ]}
                >
                  {label}
                </Text>
              </View>
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

const ERPTabNavigator = () => {
  return (
    <Tab.Navigator
      initialRouteName={strings.Home.DASHBOARD}
      tabBar={(props) => <CustomTabBar {...props} />}
      screenOptions={{ headerShown: false }}
    >
      {/* <Tab.Screen name={strings.Home.BACK} component={Home} /> */}
      <Tab.Screen name={strings.Home.DASHBOARD} component={Dashboard} />
      <Tab.Screen name={strings.Home.PROFILE} component={Profile} />
    </Tab.Navigator>
  );
};

// Light mode styles
const lightStyles = StyleSheet.create({
  tabBarContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: PrimaryColors.WHITE,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
    minHeight: Platform.OS === 'android' ? 80 : 100,
  },
  // dragHandleContainer: {
  //   alignItems: 'center',
  //   paddingTop: 10,
  // },
  // dragHandle: {
  //   width: 36,
  //   height: 4,
  //   backgroundColor: '#D1D5DB',
  //   borderRadius: 2,
  // },
  tabBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 10,
    paddingHorizontal: 20,
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
    minHeight: 60,
  },
  tabContent: {
    alignItems: 'center',
    position: 'relative',
  },
  tabLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 6,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    position: 'absolute',
    bottom: -8,
  },
});

// Dark mode styles
const darkStyles = StyleSheet.create({
  tabBarContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: PrimaryColors.BLACK,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
    minHeight: Platform.OS === 'android' ? 80 : 100,
  },

  tabBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 10,
    paddingHorizontal: 20,
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
    minHeight: 60,
  },
  tabContent: {
    alignItems: 'center',
    position: 'relative',
  },
  tabLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 6,
  },
 
});

export default ERPTabNavigator;