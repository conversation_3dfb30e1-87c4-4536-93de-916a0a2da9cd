import React, { useLayoutEffect } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useNavigation } from '@react-navigation/native';
import Home from '../Screens/ERP/Dashboard';
import ERPDetails from '../Screens/ERP/ERPDetails';
import SplashERP from '../Screens/ERP/SplashErp';
import ERPTabNavigator from '../route/ERPTabNavigator';
import Attendance from '../Screens/ERP/Attendance';
import Classwork from '../Screens/ERP/Classwork';
import Homework from '../Screens/ERP/Homework';
import Leave from '../Screens/ERP/Leave';
import Timetable from '../Screens/ERP/Timetable';
import Exam from '../Screens/ERP/Exam';
import Fees from '../Screens/ERP/Fees';

export type ERPStackParamList = {
  SplashERP:undefined;
  ERPHome: undefined;
  ERPDetails: undefined;

};

const Stack = createNativeStackNavigator<ERPStackParamList>();

const ERPStack = () => {



  return (
    <Stack.Navigator initialRouteName="SplashERP">
      <Stack.Screen
      name="SplashERP"
      component={SplashERP}
      options = {{headerShown:false}}
      />
      <Stack.Screen
        name="Home"
        component={ERPTabNavigator}
        options={{ headerShown: false }}
      />
        <Stack.Screen
        name="Attendance"
        component={Attendance}
        options={{ headerShown: false }}
      />
        <Stack.Screen
        name="Classwork"
        component={Classwork}
        options={{ headerShown: false }}
      />
        <Stack.Screen
        name="Homework"
        component={Homework}
        options={{ headerShown: false }}
      />
        <Stack.Screen
        name="Leave"
        component={Leave}
        options={{ headerShown: false }}
      />
        <Stack.Screen
        name="Timetable"
        component={Timetable}
        options={{ headerShown: false }}
      />
        <Stack.Screen
        name="Exam"
        component={Exam}
        options={{ headerShown: false }}
      />
        <Stack.Screen
        name="Fees"
        component={Fees}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="ERPDetails"
        component={ERPDetails}
        options={{ headerShown: false }}
      />
      
    </Stack.Navigator>
  );
};

export default ERPStack;
