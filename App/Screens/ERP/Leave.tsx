import React, { useState } from 'react';
import { View, SafeAreaView, TextInput, TouchableOpacity, Text, ScrollView, StyleSheet } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import IndexStyle from '../../Theme/IndexStyle';
import { PrimaryColors } from '../../Utils/Constants';
import CommonDateTimePicker from '../../CommonComponents/CommonDateTimePicker';
import CurvHeader from '../../CommonComponents/CurvHeader';
import strings from '../../Utils/LocalizedStrings/LocalizedStrings';

const Leave = () => {
  const { styles: themeStyles, isDarkMode } = IndexStyle();
  const navigation = useNavigation();
  
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [leaveReason, setLeaveReason] = useState<string>('');
  const [submittedData, setSubmittedData] = useState<{ startDate: string; endDate: string; reason: string }[]>([]);

  const formatDate = (date: Date | null) => {
    if (!date) return '';
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  const handleSubmit = () => {
    if (startDate && endDate && leaveReason.trim()) {
      setSubmittedData([
        ...submittedData,
        {
          startDate: formatDate(startDate),
          endDate: formatDate(endDate),
          reason: leaveReason,
        },
      ]);
      setStartDate(null);
      setEndDate(null);
      setLeaveReason('');
    }
  };

  return (
    <SafeAreaProvider>
      <SafeAreaView
        style={{
          flex: 1,
           backgroundColor: isDarkMode ? '#161616' : PrimaryColors.WHITE,
        }}
      >
        <CurvHeader
          title="Leave"
          isBack={true}
          onBackPress={() => navigation.goBack()}
        />
        <ScrollView contentContainerStyle={{ padding: 16 }}>
          <View style={[styles.formContainer,{backgroundColor:isDarkMode?'#161616':PrimaryColors.WHITE,borderWidth:0.5,borderColor:isDarkMode?'#C7C7C7':'#6E6E6E'}]}>
            <View><Text style={{color:isDarkMode?PrimaryColors.WHITE:PrimaryColors.BLACK,fontSize:20,fontWeight:'600',marginBottom:20}}>Apply Leave</Text></View>
            <View style={styles.datePickersContainer}>
              <View style={[styles.datePickerWrapper,]}>
                <CommonDateTimePicker
                  label="Start Date"
                  value={startDate}
                  innerText={formatDate(startDate) || 'Select Start Date'}
                  mode="date"
                  onChange={setStartDate}
                  minimumDate={new Date()}
                />
              </View>
              <View style={styles.datePickerWrapper}>
                <CommonDateTimePicker
                  label="End Date"
                  value={endDate}
                  innerText={formatDate(endDate) || 'Select End Date'}
                  mode="date"
                  onChange={setEndDate}
                  minimumDate={startDate || new Date()}
                 
                />
              </View>
            </View>
            <View style={styles.reasonContainer}>
              <Text style={[styles.label, { color: isDarkMode ? '#8D8D8D' : '#6E6E6E'}]}>
                Leave Reason
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  {
                    // backgroundColor: isDarkMode ? '#1B1B1B' : '#fff',
                    borderColor: isDarkMode ? '#CCCCCC' : '#ccc',
                    color: isDarkMode ? PrimaryColors.WHITE : '#333',
                  },
                ]}
                placeholder="Enter reason for leave"
                placeholderTextColor={isDarkMode ? PrimaryColors.GRAYSHADOW : '#999'}
                value={leaveReason}
                onChangeText={setLeaveReason}
                multiline
              />
            </View>
            <TouchableOpacity
              style={[
                styles.submitButton,
                {
                  backgroundColor: !startDate || !endDate || !leaveReason.trim()
                    ? isDarkMode ? '#FF914D' : '#FF914D'
                    : '#FF914D',
                },
              ]}
              onPress={handleSubmit}
              disabled={!startDate || !endDate || !leaveReason.trim()}
            >
              <Text style={styles.submitButtonText}>Submit</Text>
            </TouchableOpacity>
          </View>
          {submittedData.length > 0 && (
            <View style={styles.submittedDataContainer}>
              <View style={{flexDirection:'row'}}>
              <Text style={[styles.sectionTitle, { color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,marginRight:8 }]}>
                Applied 
              </Text>
              <Text style={[styles.sectionTitle, { color: '#FF914D' }]}>
                Leaves
              </Text>
              </View>
              {submittedData.map((data, index) => (
                <View
                  key={index}
                  style={[
                    styles.submittedItem,
                    {borderColor:'#6E6E6E'}
                    
                  ]}
                >
                  <View style={{flexDirection:'row',justifyContent:'space-between'}}>
                  <Text style={[styles.submittedText, { color: isDarkMode ? PrimaryColors.WHITE : '#333',paddingTop:3,marginBottom:6 }]}>
                    <Text style={styles.submittedLabel}>Start Date : </Text><Text style={{color:isDarkMode?'#8E8E8E':'#332F2F'}}>{data.startDate}</Text>
                  </Text>
                  <Text style={{fontSize:10,textAlign:'right',color:'#6E6E6E'}}>Applied on 18 June 2025</Text>

                  </View>
                  <Text style={[styles.submittedText, { color: isDarkMode ? PrimaryColors.WHITE : '#333',marginBottom:6 }]}>
                    <Text style={styles.submittedLabel}>End Date : </Text><Text style={{color:isDarkMode?'#8E8E8E':'#332F2F'}}>{data.endDate}</Text> 
                  </Text>
                  <Text style={[styles.submittedText, { color: isDarkMode ? PrimaryColors.WHITE : '#333',marginBottom:6 }]}>
                    <Text style={styles.submittedLabel}>Reason : </Text> <Text style={{color:isDarkMode?'#8E8E8E':'#332F2F'}}>{data.reason}</Text>
                  </Text>
                </View>
              ))}
            </View>
          )}
        </ScrollView>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

const styles = StyleSheet.create({
  formContainer: {
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 16,
    marginBottom: 16,
    // shadowColor: '#000',
    // shadowOffset: { width: 0, height: 2 },
    // shadowOpacity: 0.1,
    // shadowRadius: 5,
    // elevation: 3,
  },
  datePickersContainer: {
    // flexDirection: 'row',
    // justifyContent: 'space-between',
    // gap: 12,
  },
  datePickerWrapper: {
    width:'125%'
  },
  reasonContainer: {
    marginTop: 3,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  submitButton: {
    marginTop: 16,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  submitButtonText: {
    color: PrimaryColors.WHITE,
    fontSize: 16,
    fontWeight: '600',
  },
  submittedDataContainer: {
    marginTop: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  submittedItem: {
   paddingHorizontal:16,
    paddingTop:10,
    borderRadius: 8,
    marginBottom: 12,
    borderWidth: 0.5,
    paddingBottom:10
  
  },
  submittedText: {
    fontSize: 14,
    marginBottom: 4,
  },
  submittedLabel: {
    fontWeight: '500',
  },
});

export default Leave;