import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import IndexStyle from '../../Theme/IndexStyle';
import { PrimaryColors } from '../../Utils/Constants';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import CurvHeader from '../../CommonComponents/CurvHeader';
import CalendarStrip from 'react-native-calendar-strip';
import moment from 'moment';

const Timetable = () => {
  const navigation = useNavigation();
  const { isDarkMode } = IndexStyle();
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);

  const today = new Date();

  const dates = [];
  for (let i = -3; i <= 3; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + i);
    dates.push(date);
  }

  const formattedDates = dates.map((date) => {
    const day = date.getDate();
    const weekday = date.toLocaleString('default', { weekday: 'short' });
    const dateString = date.toISOString().split('T')[0];
    const isSunday = date.getDay() === 0;

    return {
      day,
      weekday,
      dateString,
      isSunday,
    };
  });

  const getDateDisplayText = (selectedDate) => {
    const today = new Date();
    const selected = new Date(selectedDate);
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    const isSameDay = (date1, date2) =>
      date1.getDate() === date2.getDate() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getFullYear() === date2.getFullYear();

    if (isSameDay(selected, today)) {
      return 'Today';
    } else if (isSameDay(selected, yesterday)) {
      return 'Yesterday';
    } else if (isSameDay(selected, tomorrow)) {
      return 'Tomorrow';
    } else {
      const day = selected.getDate();
      const month = selected.toLocaleString('default', { month: 'short' });
      let suffix = 'th';
      if (day % 10 === 1 && day !== 11) suffix = 'st';
      else if (day % 10 === 2 && day !== 12) suffix = 'nd';
      else if (day % 10 === 3 && day !== 13) suffix = 'rd';
      return `${day}${suffix} ${month}`;
    }
  };

  const timetableData = [
    {
      time: '09:00 AM to 09:40 AM',
      subject: 'Mathematics',
      teacher: 'Swami Vivekanand',
      date: '16 June 2025 Monday',
      isBreak: false,
    },
    {
      time: '09:40 AM to 10:30 AM',
      subject: 'English',
      teacher: 'Swami Vivekanand',
      date: '16 June 2025 Monday',
      isBreak: false,
    },
    {
      time: '10:30 AM to 11:00 AM',
      subject: 'Breakfast',
      teacher: '',
      date: '16 June 2025 Monday',
      isBreak: true,
    },
    {
      time: '11:00 AM to 12:00 PM',
      subject: 'Sanskrit',
      teacher: 'Swami Vivekanand',
      date: '16 June 2025 Monday',
      isBreak: false,
    },
    {
      time: '12:00 PM to 01:00 PM',
      subject: 'Hindi',
      teacher: 'Swami Vivekanand',
      date: '16 June 2025 Monday',
      isBreak: false,
    },
    {
      time: '01:00 AM to 02:40 AM',
      subject: 'Mathematics',
      teacher: 'Swami Vivekanand',
      date: '16 June 2025 Monday',
      isBreak: false,
    },
    {
      time: '02:40 AM to 03:30 AM',
      subject: 'English',
      teacher: 'Swami Vivekanand',
      date: '16 June 2025 Monday',
      isBreak: false,
    },
    {
      time: '10:30 AM to 11:00 AM',
      subject: 'Breakfast',
      teacher: '',
      date: '16 June 2025 Monday',
      isBreak: true,
    },
  ];

  return (
    <SafeAreaProvider>
      <SafeAreaView
        style={{flex: 1,backgroundColor: isDarkMode ? '#161616' : PrimaryColors.WHITE,}}
        edges={['left', 'right']}
      >
        <CurvHeader
          title="TimeTable"
          isBack={true}
          onBackPress={() => {
            navigation.goBack();
          }}
        />

<View style={styles.calendarContainer}>

<CalendarStrip
  scrollable={true}
  style={[styles.calendarStrip,{backgroundColor:isDarkMode?'#161616':PrimaryColors.WHITE}]}
  calendarHeaderStyle={{ display: 'none' }}
  dateNumberStyle={[styles.dateNumber,{color:isDarkMode?PrimaryColors.WHITE:PrimaryColors.BLACK}]}
  dateNameStyle={[styles.dateName,{color:isDarkMode?PrimaryColors.WHITE:PrimaryColors.BLACK}]}
  highlightDateNumberStyle={styles.highlightDateNumber}
  highlightDateNameStyle={styles.highlightDateName}
  disabledDateNameStyle={styles.disabledDateName}
  disabledDateNumberStyle={styles.disabledDateNumber}
  selectedDate={moment(selectedDate)}
  onDateSelected={(date) => {
    setSelectedDate(date.format('YYYY-MM-DD'));
  }}
  minDate={moment('2020-01-01')}
  maxDate={moment('2030-12-31')}
  calendarColor={'#fff'}
  leftSelector={[]}
  rightSelector={[]}
  dayContainerStyle={styles.dayContainer}
/>
</View>

        {/* Display the selected date text */}
        <View style={styles.dateDisplayContainer}>
          <Text style={[styles.dateDisplayText,{color:isDarkMode?PrimaryColors.WHITE:PrimaryColors.BLACK}]}>{getDateDisplayText(selectedDate)}</Text>
        </View>

        {/* Timetable Cards ScrollView */}
        <ScrollView
        showsVerticalScrollIndicator={false}
          style={styles.timetableScrollView}
          contentContainerStyle={styles.timetableContentContainer}
        >
          {timetableData.map((entry, index) => (
            <TouchableOpacity key={index} style={[styles.timetableCard,{backgroundColor:isDarkMode?PrimaryColors.BLACK:PrimaryColors.WHITE,borderColor: isDarkMode ? "#444" : '#CCCCCC'}]}>
              <View style={styles.cardContent}>
                <View
                  style={[
                    styles.timeContainer,
                    entry.isBreak ? {backgroundColor:'#FF914D'}: {backgroundColor:isDarkMode?'#161616':PrimaryColors.BLACK}                  ]}
                >
                  <Text style={styles.timeText}>{entry.time}</Text>
                </View>
                <View style={styles.detailsContainer}>
                  <Text style={[styles.subjectText,{color:isDarkMode?PrimaryColors.WHITE:PrimaryColors.BLACK}]}>{entry.subject}</Text>
                  {!entry.isBreak && (
                    <>
                      <Text style={styles.teacherText}>{entry.teacher}</Text>
                      <Text style={[styles.dateText,{color:isDarkMode?'#777':PrimaryColors.BLACK}]}>{entry.date}</Text>
                    </>
                  )}
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: PrimaryColors.WHITE,
  },
  calendarContainer: {
    marginHorizontal: 10,
    marginVertical: 10,
    borderRadius: 10,
    backgroundColor: '#fff',
    // borderColor: '#ccc',
  },
  calendarStrip: {
    height: 90, // Increased height to accommodate the selected date
    paddingTop: 10,
    paddingBottom: 10,
  },
  dayContainer: {
    width: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 2,
  },
  dateNumber: {
    fontSize: 16,
    color: '#000',
    fontWeight: 'bold',
  },
  dateName: {
    fontSize: 12,
    color: '#000',
    marginTop: 2, // Added spacing between date number and name
  },
  highlightDateNumber: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: 'bold',
    backgroundColor: '#FF7825',
    borderRadius: 16,
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  highlightDateName: {
    fontSize: 12,
    color: '#FF7825',
    marginTop: 2, // Added spacing between date number and name
  },
  disabledDateName: {
    fontSize: 12,
    color: '#d9e1e8',
    marginTop: 2,
  },disabledDateNumber: {
    fontSize: 16,
    color: '#d9e1e8',
  },
  datePickerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    backgroundColor: '#fff',
    borderRadius: 10,
  },
  dateListContainer: {
    paddingHorizontal: 16,
    paddingVertical: 15,
    justifyContent: 'center',
  },
  dateCard: {
    width: 40,
    height: 40,
    marginHorizontal: 4,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sundayText: {
    color: '#FF7825',
  },
  selectedCard: {
    backgroundColor: '#FF7825',
    borderRadius: 20,
  },
  selectedText: {
    color: '#FFFFFF',
  },
  dateText: {
    fontSize: 12,
  },
  weekdayText: {
    fontSize: 12,
    fontWeight: '400',
    color: '#000',
    marginTop: 3,
    textAlign: 'center',
  },
  calendar: {
    borderRadius: 20,
  },
  summaryContainer: {
    marginHorizontal: 16,
    marginBottom: 70,
  },
  summaryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 20,
    marginBottom: 10,
    borderLeftWidth: 2,
    borderLeftColor: '#ccc',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: -2, height: 0 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  summaryText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000',
  },
  countContainer: {
    width: 30,
    height: 30,
    borderRadius: 15,
    borderWidth: 2,
    borderColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  count: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#000',
  },
  dateDisplayContainer: {
    alignItems: 'flex-start',
    marginTop: 10,
    marginLeft: 20,
  },
  dateDisplayText: {
    fontSize: 20,
    fontWeight: '700',
    color: '#000',
  },
  // New styles for timetable cards
  timetableScrollView: {
    flex: 1,
    marginHorizontal: 20,
    marginTop:16,
  },
  timetableContentContainer: {
    paddingBottom: 20,
  },
  timetableCard: {
    backgroundColor: '#fff',
    borderRadius: 15,
    marginBottom: 15,
    padding: 15,
    borderColor:'#E5D7D7',
    borderWidth:1
    // elevation: 3,
    // shadowColor: '#000',
    // shadowOffset: { width: 0, height: 2 },
    // shadowOpacity: 0.2,
    // shadowRadius: 4,
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeContainer: {
    borderRadius: 20,
    paddingVertical: 5,
    paddingHorizontal: 10,
    marginRight: 15,
  },
  classTimeContainer: {
    backgroundColor: '#000',
    borderRadius:10
  },
  breakTimeContainer: {
    backgroundColor: '#FF7825',
  },
  timeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  detailsContainer: {
    flex: 1,
  },
  subjectText: {
    fontSize: 18,
    fontWeight: '700',
    color: '#000',
    marginBottom: 5,
  },
  teacherText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#555',
    marginBottom: 3,
  },

});

export default Timetable;