import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  FlatList,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import { PrimaryColors,IMAGE_CONSTANT } from '../../Utils/Constants';
import IndexStyle from '../../Theme/IndexStyle';
const { width,height } = Dimensions.get('window');

const categories = [
  { title: 'Attendance', image:IMAGE_CONSTANT.ATTENDENCE ,route:'Attendance' },
  { title: 'Classwork',image:IMAGE_CONSTANT.CLASSWORK, route:'Classwork'},
  { title: 'Homework', image:IMAGE_CONSTANT.HOMEWORK,route:'Homework' },
  { title: 'Leaves', image:IMAGE_CONSTANT.LEAVES ,route:'Leave'},
  { title: 'Timetable',image:IMAGE_CONSTANT.TIMETABLE,route:'Timetable' },
  { title: 'Exam', image:IMAGE_CONSTANT.EXAMCARD,route:'' },
  { title: 'Fees',image:IMAGE_CONSTANT.FEES,route:'' },
];

const Dashboard = ({ navigation }) => {
  const {isDarkMode} = IndexStyle();
  const [profileDetails,setProfileDetails] = useState({});

   useEffect(() => {
     getStudentDetails();
    }, []);

  const getStudentDetails = () => {
    var response = {
      "success": true,
      "message": "Student Details Fetched Successfully",
      "data": {
      "personal_details": {
      "gr_no": "77897897",
      "admission_category": "Non RTE",
      "first_name": "gfdg",
      "middle_name": "gcxg",
      "last_name": "cgxgxc",
      "gender": "Male",
      "date_of_birth": "2025-06-12",
      "mother_tongue": "hffhg",
      "address": "efrh",
      "pin": null,
      "religion": null,
      "caste": null,
      "contact_no": 7894561230,
      "email": "<EMAIL>",
      "photo": null
      },
      "academic_info": {
      "department": "Education",
      "classroom": "fdsfsd",
      "year": "2025-2026",
      "status": "ACTIVE",
      "class_teacher_name": null,
      "class_teacher_contact": "656565456465"
      },
      "transport_info": {
      "waypoint_name": "umiyaji circle",
      "waypoint_fee": "5000.00",
      "route_name": "ravapar road",
      "route_no": "56",
      "vehicle_no": "hj78945",
      "driver_name": null,
      "driver_contact_no": null
      },
      "parent_info": {
      "father_name": "tkrk hfgf jhghjg",
      "mother_name": "hjgfv kjgv jhgh",
      "contact_no_1": **********,
      "contact_no_2": null,
      "part_of_ngo": null
      },
      "siblings_info": {
      "sibling_name": null,
      "studying_std": null,
      "school_name": null
      },
      "health_info": {
      "eye_sight": null,
      "hear_ability": null,
      "allergy_1": null,
      "allergy_2": null,
      "any_health_issue": null,
      "doctors_name": null
      },
      "past_info": {
      "prev_standard": "geh",
      "prev_school": "hdhd",
      "prev_passing_year": "7894",
      "prev_school_left_date": "2025-06-18",
      "left_reason": "hdfhg"
      }
      }
     };
    setProfileDetails(response.data);
  }
  const renderItem = ({ item }) => (
    <TouchableOpacity style={[Basestyles.card,{backgroundColor:isDarkMode ? '#313131' :PrimaryColors.WHITE}]} onPress={()=>{navigation.navigate(item.route)}}>
      <Image source={item.image} style={Basestyles.cardImage} />

      <Text style={[Basestyles.cardTitle,{color:isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK}]}>{item.title}</Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaProvider style={{backgroundColor:PrimaryColors.BLACK}}>
      <SafeAreaView style={[Basestyles.container,{backgroundColor:PrimaryColors.BLACK}]} edges={['left','right']}>
        <View style={Basestyles.header}>
          <View style={{}}>
          <Text style={Basestyles.dashboardTitle}>Dashboard</Text>
          <View style={Basestyles.userRow}>
            <View style={Basestyles.userInfo}>
              <Text style={[Basestyles.welcomeText,]}>Welcome,</Text>
              <Text style={[Basestyles.nameText,{paddingTop:4}] }>{profileDetails?.personal_details?.first_name} {profileDetails?.personal_details?.last_name}</Text>
              <View style={{paddingTop:10}}>
              <Text style={Basestyles.detailText}>Class : {profileDetails?.academic_info?.classroom}</Text>
              <Text style={Basestyles.detailText}>Class Teacher : {profileDetails?.academic_info?.class_teacher_name}</Text>
              <Text style={Basestyles.detailText}>Contact no : {profileDetails?.personal_details?.contact_no}</Text>
              </View>
            </View>
            <View>
            <Image
              source={{ uri: 'https://i.pravatar.cc/200' }}
              style={Basestyles.avatar}
            />
            </View>
          </View>
        </View>
        </View>
        <View style={[Basestyles.gridContainer,{backgroundColor:isDarkMode?'#161616':PrimaryColors.WHITE}]}>
        <View style={{marginBottom:20}}><Text style={{fontSize:18,color:isDarkMode?PrimaryColors.WHITE:'#505050',fontWeight:'medium',letterSpacing:0.2}}>Quick Links</Text></View>
          <FlatList
            data={categories}
            renderItem={renderItem}
            keyExtractor={(item, index) => index.toString()}
            numColumns={3}
            columnWrapperStyle={{ justifyContent: 'space-around' }}
            contentContainerStyle={{ paddingBottom: 40 }}
          />
        </View>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

const Basestyles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    height:height * 0.32,
    paddingTop:'12%',
    padding: 16,
  },
  dashboardTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 10,
    textAlign:'center',
  },
  userRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop:'3%',
  },
  userInfo: {
    flex: 1,
    marginRight: 10,
  },
  welcomeText: {
    color: '#FF914D',
    fontSize: 20,
  },
  nameText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
    paddingTop:2,
  },
  detailText: {
    color: '#969696',
    fontSize: 14,
    marginBottom:5,
    letterSpacing:0.2,
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
  },
  gridContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 20,
    height:height * 0.68,
    borderTopLeftRadius:20,
    borderTopRightRadius:20,
    justifyContent:'space-around',
  },
  card: {
    height:height * 0.12,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 20,
    width: (width - 64) / 3,
    elevation: 4,
    shadowColor:PrimaryColors.BLACK,
    shadowOpacity: 0.5,
    shadowOffset: { width: 0, height: 0 },
    shadowRadius: 4,
  },
  cardImage: {
    width: '60%',
    height: '75%',
    resizeMode:'contain',
  },
  cardTitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom:'2%',
  },
});

export default Dashboard;
