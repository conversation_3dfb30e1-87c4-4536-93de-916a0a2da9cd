import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity } from 'react-native';
import { Calendar } from 'react-native-calendars';
import { useNavigation } from '@react-navigation/native';
import IndexStyle from '../../Theme/IndexStyle';
import { PrimaryColors } from '../../Utils/Constants';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import CurvHeader from '../../CommonComponents/CurvHeader';

const Attendance = () => {
  const navigation = useNavigation();
  const { isDarkMode } = IndexStyle();
  const [attendance, setAttendance] = useState({});
  const [markedDates, setMarkedDates] = useState({});
  const [summaryCounts, setSummaryCounts] = useState({
    absent: 0,
    present: 0,
    leave: 0,
    discipline: 0,
  });

  useEffect(() => {
    getAttendance();
  }, []);

  const getAttendance = () => {
    var response = {
      "success": true,
      "message": "Attendance Fetched Successfully",
      "data": {
        "2025-06-02": { "status": "absent" },
        "2025-06-04": { "status": "absent" },
        "2025-06-06": { "status": "present" },
        "2025-06-08": { "status": "leave" },
        "2025-06-25": { "status": "discipline" },
      }
    };
    setAttendance(response.data);
    transformAttendanceData(response.data);
  };

  const transformAttendanceData = (data) => {
    const newMarkedDates = {};
    const counts = {
      absent: 0,
      present: 0,
      leave: 0,
      discipline: 0,
    };

    Object.keys(data).forEach((date) => {
      const status = data[date].status;
      counts[status] = (counts[status] || 0) + 1;

      newMarkedDates[date] = {
        customStyles: {
          container: {
            borderRadius: 20,
            backgroundColor: getBackgroundColor(status),
          },
          text: { color: '#fff' },
        },
      };
    });

    setMarkedDates(newMarkedDates);
    setSummaryCounts(counts);
  };

  const getBackgroundColor = (status) => {
    switch (status) {
      case 'absent':
        return '#E53935'; // Red
      case 'present':
        return '#43A047'; // Green
      case 'leave':
        return '#F9A825'; // Yellow
      case 'discipline':
        return '#1E88E5'; // Blue
      default:
        return '#ccc'; // Fallback color
    }
  };

  return (
    <SafeAreaProvider>
      <SafeAreaView
        style={{
          flex: 1,
          backgroundColor: isDarkMode ? '#161616' : PrimaryColors.WHITE,
        }}
        edges={['left', 'right']}
      >
        <CurvHeader
          title="Attendance"
          isBack={true}
          onBackPress={() => {
            navigation.goBack();
          }}
        />
        <View style={[styles.calendarContainer, { backgroundColor: isDarkMode ? '#161616' : PrimaryColors.WHITE }]}>
          <Calendar
           // Set to June 2025 to show the response data
            markingType={'custom'}
            markedDates={markedDates}
            theme={{
              calendarBackground: isDarkMode ? '#161616' : PrimaryColors.WHITE,
              textSectionTitleColor: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
              dayTextColor: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
              todayTextColor: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
              arrowColor: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
              monthTextColor: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
              textDayFontSize: 16,
              textMonthFontSize: 18,
              textDayHeaderFontSize: 14,
            }}
            style={styles.calendar}
          />
        </View>

        {/* Summary Section */}
        <View style={styles.summaryContainer}>
          {/* Absent */}
          <TouchableOpacity style={[styles.summaryItem, { backgroundColor: '#FDEAEA', borderLeftColor: '#B71C1C', borderColor: '#B71C1C', borderWidth: 0.5 }]}>
            <Text style={styles.summaryText}>Absent</Text>
            <View style={[styles.countContainer, { borderLeftColor: '#B71C1C' }]}>
              <Text style={styles.count}>{summaryCounts.absent}</Text>
            </View>
          </TouchableOpacity>

          {/* Leave */}
          <TouchableOpacity style={[styles.summaryItem, { backgroundColor: '#FFF8E1', borderLeftColor: '#FBC02D', borderColor: '#F9A825', borderWidth: 0.5 }]}>
            <Text style={styles.summaryText}>Leave</Text>
            <View style={[styles.countContainer, { borderLeftColor: '#F9A825' }]}>
              <Text style={styles.count}>{summaryCounts.leave}</Text>
            </View>
            </TouchableOpacity>

          {/* Present */}
          <TouchableOpacity style={[styles.summaryItem, { backgroundColor: '#E8F5E9', borderLeftColor: '#388E3C', borderColor: '#1B5E20', borderWidth: 0.5 }]}>
            <Text style={styles.summaryText}>Present</Text>
            <View style={[styles.countContainer, { borderLeftColor: '#43A047' }]}>
              <Text style={styles.count}>{summaryCounts.present}</Text>
            </View>
          </TouchableOpacity>

          {/* Discipline */}
          <TouchableOpacity style={[styles.summaryItem, { backgroundColor: '#E3F2FD', borderLeftColor: '#1976D2', borderColor: '#0D47A1', borderWidth: 0.5 }]}>
            <Text style={styles.summaryText}>Discipline</Text>
            <View style={[styles.countContainer, { borderLeftColor: '#1E88E5' }]}>
              <Text style={styles.count}>{summaryCounts.discipline}</Text>
            </View>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: PrimaryColors.WHITE,
  },
  calendarContainer: {
    margin: 8,
    overflow: 'hidden',
  },
  calendar: {
    borderRadius: 20,
  },
  summaryContainer: {
    marginHorizontal: 16,
    marginTop: 32,
  },
  summaryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 20,
    marginBottom: 10,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: -8, height: 0 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  summaryText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000',
  },
  countContainer: {
    width: 30,
    height: 30,
    borderRadius: 15,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  count: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#000',
  },
});

export default Attendance;