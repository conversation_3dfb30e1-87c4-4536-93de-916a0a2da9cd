import AsyncStorage from '@react-native-async-storage/async-storage';
import {ErpBaseUrl,} from './apiUrl';

export default { 
    StudentDetails: {
      studentDetails: async (email:string) => {
        try {
          const token = await AsyncStorage.getItem('token');
          const url = `${ErpBaseUrl}get-student-details`;
          const opt = {
            method: 'GET',
            headers: {
              'ContentType': 'application/json',
              'Set-Cookie': `XSRF-TOKEN=${token}`,
              // Accept: '*/*',
              // 'client-type': 'mobile-app',
              // Authorization: `Bearer ${token}`, // Optional, only if backend expects it
            },
            body: JSON.stringify({ "email" :email }),
          };
          console.log('Fetching student details...');
          return fetch(url, opt);
        } catch (error) {
          console.error('student details fetch error:', error);
          throw error;
        }
      },
      },
      ClassworkDetails: {
        classWork: async () => {
          try {
            const token = await AsyncStorage.getItem('token'); // your app auth token if needed
            const url = `${ErpBaseUrl}classWork`;
            const opt = {
              method: 'GET',
              headers: {
                'content-type': 'application/json',
                // Accept: '*/*',
                // 'client-type': 'mobile-app',
                'Set-Cookie': `XSRF-TOKEN=${token}`,
                // Authorization: `Bearer ${token}`, // Optional, only if backend expects it
              },
            };
      
            console.log('Fetching classWork...');
            return fetch(url, opt);
          } catch (error) {
            console.error('classWork fetch error:', error);
            throw error;
          }
        },
      },
      HomeworkDetails: {
        homework: async () => {
          try {
            const token = await AsyncStorage.getItem('token'); // your app auth token if needed
            const url = `${ErpBaseUrl}homeWork`;
            const opt = {
              method: 'GET',
              headers: {
                'content-type': 'application/json',
                // Accept: '*/*',
                // 'client-type': 'mobile-app',
                'Set-Cookie': `XSRF-TOKEN=${token}`,
                // Authorization: `Bearer ${token}`, // Optional, only if backend expects it
              },
            };
      
            console.log('Fetching homework...');
            return fetch(url, opt);
          } catch (error) {
            console.error('homework fetch error:', error);
            throw error;
          }
        },
      },
      TimeTable:{
        getTimeTable: async (email:string,date:string) => {
          try {
            const token = await AsyncStorage.getItem('token');
            const url = `${ErpBaseUrl}get-timetable`;
            const opt = {
              method: 'GET',
              headers: {
                'ContentType': 'application/json',
                'Set-Cookie': `XSRF-TOKEN=${token}`,
                // Accept: '*/*',
                // 'client-type': 'mobile-app',
                // Authorization: `Bearer ${token}`, // Optional, only if backend expects it
              },
              body: JSON.stringify({ "email":email,"date":date }),
            };
            console.log('Fetching student details...');
            return fetch(url, opt);
          } catch (error) {
            console.error('student details fetch error:', error);
            throw error;
          }
        },
      },
      Attendance:{
        getAttendance: async (email: string, startDate: string, endDate: string) => {
          try {
            const token = await AsyncStorage.getItem('token');
            const url = `${ErpBaseUrl}get-attendance`;
            const opt = {
              method: 'GET',
              headers: {
                'content-type': 'application/json',
                'Set-Cookie': `XSRF-TOKEN=${token}`,
                // Accept: '*/*',
                // 'client-type': 'mobile-app',
                // Authorization: `Bearer ${token}`, // Optional, only if backend expects it
              },
              body: JSON.stringify({ "email":email, "startDate":startDate, "endDate":endDate }),
            };
            console.log('Fetching attendance...');
            return fetch(url, opt);
          } catch (error) {
            console.error('attendance fetch error:', error);
            throw error;
          }
        },

        
        getDicipline: async (email: string, startDate: string, endDate: string) => {
          try {
            const token = await AsyncStorage.getItem('token');
            const url = `${ErpBaseUrl}get-discipline-issue`;
            const opt = {
              method: 'GET',
              headers: {
                'content-type': 'application/json',
                'Set-Cookie': `XSRF-TOKEN=${token}`,
              },
              body: JSON.stringify({ "email":email, "startDate":startDate, "endDate":endDate }),
            };
            console.log('Fetching dicipline...');
            return fetch(url, opt);
          } catch (error) {
            console.error('dicipline fetch error:', error);
            throw error;
          }
        },
      }
}