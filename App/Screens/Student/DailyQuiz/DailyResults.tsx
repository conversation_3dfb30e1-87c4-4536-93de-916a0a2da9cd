import React, { useEffect, useState } from 'react';
import {
    View,
    Text,
    StyleSheet,
    FlatList,
    ActivityIndicator,
    RefreshControl,
} from 'react-native';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';

import NavigationHeader from '../../../CommonComponents/NavigationHeader';
import IndexStyle from '../../../Theme/IndexStyle';
import { getDailyResults } from '../../../services/dailyQuizService';
import { getThemeColors } from '../../../Utils/Constants';

interface DailyResult {
    id: string;
    studentId: string;
    score: number;
    coinEarnings: number;
    streakId: string;
    createdAt: string;
    updatedAt: string;
}

interface StreakInfo {
    streakCount: number;
    lastAttempt: string;
}

interface BadgeInfo {
    streakCount: number;
    badges: Array<{
        badgeType: string;
        badgeSrc: string;
        badgeAlt: string;
        count: number;
    }>;
}

interface DailyResultsData {
    mockExamResults: DailyResult[];
    streak: StreakInfo;
    badge: BadgeInfo;
}

const LIMIT = 10;

export default function DailyResults() {
    const { isDarkMode } = IndexStyle();
    const [resultsData, setResultsData] = useState<DailyResultsData | null>(null);
    const [page, setPage] = useState(1);
    const [isLoading, setIsLoading] = useState(false);
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const [studentId, setStudentId] = useState<string>('');

    const themeColors = getThemeColors(isDarkMode);

    useEffect(() => {
        const getStudentId = async () => {
            try {
                const id = await AsyncStorage.getItem('studentId');
                if (id) {
                    setStudentId(id);
                    fetchResults(id, 1, true);
                }
            } catch (error) {
                console.error('Error getting student ID:', error);
            }
        };

        getStudentId();
    }, []);

    const fetchResults = async (id: string, pg: number, reset = false) => {
        if (isLoading) return;
        if (!reset && !hasMore) return;

        setIsLoading(true);

        try {
            const response = await getDailyResults(id, pg, LIMIT);
            const fetchedData = response.data;

            if (reset) {
                setResultsData(fetchedData);
            } else {
                setResultsData(prev => prev ? {
                    ...fetchedData,
                    mockExamResults: [...prev.mockExamResults, ...fetchedData.mockExamResults]
                } : fetchedData);
            }

            setHasMore(fetchedData.mockExamResults.length === LIMIT);
            setPage(reset ? 2 : pg + 1);
        } catch (error) {
            console.error('Error fetching daily results:', error);
        } finally {
            setIsLoading(false);
            setIsRefreshing(false);
        }
    };

    const onRefresh = () => {
        if (studentId) {
            setIsRefreshing(true);
            setPage(1);
            setHasMore(true);
            fetchResults(studentId, 1, true);
        }
    };

    const loadMore = () => {
        if (studentId && hasMore && !isLoading) {
            fetchResults(studentId, page);
        }
    };

    const getScoreColor = (score: number) => {
        if (score >= 90) return themeColors.accent;
        if (score >= 80) return themeColors.text;
        if (score >= 70) return themeColors.greyDark;
        return themeColors.muted;
    };

    const getScoreIcon = (score: number) => {
        if (score >= 90) return 'star';
        if (score >= 80) return 'trending-up';
        if (score >= 70) return 'trending-flat';
        return 'trending-down';
    };

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
        });
    };

    const formatTime = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const getStreakColor = (streakCount: number) => {
        if (streakCount >= 14) return themeColors.accent;
        if (streakCount >= 7) return isDarkMode ? '#E0E0E0' : '#2D2D2D';
        if (streakCount >= 3) return isDarkMode ? '#B0B0B0' : '#5A5A5A';
        return themeColors.muted;
    };

    const renderResultItem = ({ item }: { item: DailyResult }) => (
        <View style={[styles.resultItem, {
            backgroundColor: themeColors.card,
            borderColor: themeColors.border,
            shadowColor: themeColors.shadow,
        }]}>
            {/* Left Section: Date & Time */}
            <View style={styles.leftSection}>
                <Text style={[styles.dateText, { color: themeColors.text }]}>
                    {formatDate(item.createdAt)}
                </Text>
                <Text style={[styles.timeText, { color: themeColors.muted }]}>
                    {formatTime(item.createdAt)}
                </Text>
            </View>

            {/* Middle Section: Score */}
            <View style={styles.middleSection}>
                <View style={[styles.scoreContainer, {
                    backgroundColor: getScoreColor(item.score) + '20',
                }]}>
                    <MaterialIcons 
                        name={getScoreIcon(item.score)} 
                        size={20} 
                        color={getScoreColor(item.score)} 
                    />
                    <Text style={[styles.scoreText, { color: getScoreColor(item.score) }]}>
                        {item.score}%
                    </Text>
                </View>
            </View>

            {/* Right Section: Coins */}
            <View style={styles.rightSection}>
                <View style={[styles.coinsBadge, { backgroundColor: themeColors.greyLight }]}>
                    <View style={styles.uestCoinIcon}>
                        <Text style={styles.uestCoinText}>U</Text>
                    </View>
                    <Text style={[styles.coinsText, { color: themeColors.text }]}>
                        {item.coinEarnings}
                    </Text>
                </View>
            </View>
        </View>
    );

    const renderStreakHeader = () => {
        if (!resultsData?.streak) return null;

        const { streakCount } = resultsData.streak;
        const streakColor = getStreakColor(streakCount);

        return (
            <View style={[styles.streakHeader, {
                backgroundColor: themeColors.card,
                borderColor: themeColors.border,
            }]}>
                <View style={styles.streakContent}>
                    <MaterialIcons name="local-fire-department" size={32} color={streakColor} />
                    <View style={styles.streakInfo}>
                        <Text style={[styles.streakTitle, { color: themeColors.text }]}>
                            Daily Streak
                        </Text>
                        <Text style={[styles.streakCount, { color: streakColor }]}>
                            {streakCount} {streakCount === 1 ? 'day' : 'days'}
                        </Text>
                    </View>
                </View>
                
                {resultsData.badge.badges.length > 0 && (
                    <View style={styles.badgeContainer}>
                        <MaterialIcons name="emoji-events" size={24} color={themeColors.accent} />
                        <Text style={[styles.badgeText, { color: themeColors.accent }]}>
                            {resultsData.badge.badges.length} Badge{resultsData.badge.badges.length !== 1 ? 's' : ''}
                        </Text>
                    </View>
                )}
            </View>
        );
    };

    return (
        <SafeAreaProvider>
            <NavigationHeader title="Daily Results" onBackPress={() => {}} />
            <SafeAreaView
                style={[styles.container, { backgroundColor: themeColors.background }]}
                edges={['left', 'right']}>
                
                <FlatList
                    data={resultsData?.mockExamResults || []}
                    renderItem={renderResultItem}
                    keyExtractor={(item) => item.id}
                    ListHeaderComponent={renderStreakHeader}
                    onEndReached={loadMore}
                    onEndReachedThreshold={0.3}
                    refreshControl={
                        <RefreshControl
                            refreshing={isRefreshing}
                            onRefresh={onRefresh}
                            colors={[themeColors.accent]}
                            tintColor={themeColors.accent}
                        />
                    }
                    ListFooterComponent={
                        isLoading ? (
                            <View style={styles.loadingContainer}>
                                <ActivityIndicator size="large" color={themeColors.accent} />
                            </View>
                        ) : null
                    }
                    ListEmptyComponent={
                        !isLoading ? (
                            <View style={styles.emptyContainer}>
                                <MaterialIcons name="quiz" size={64} color={themeColors.muted} />
                                <Text style={[styles.emptyText, { color: themeColors.muted }]}>
                                    No quiz results yet
                                </Text>
                                <Text style={[styles.emptySubtext, { color: themeColors.muted }]}>
                                    Take your first daily quiz to see results here
                                </Text>
                            </View>
                        ) : null
                    }
                    contentContainerStyle={styles.listContainer}
                />
            </SafeAreaView>
        </SafeAreaProvider>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    listContainer: {
        padding: 16,
        paddingBottom: 80,
    },

    // Streak Header
    streakHeader: {
        padding: 20,
        marginBottom: 20,
        borderRadius: 16,
        borderWidth: 1,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    streakContent: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
    },
    streakInfo: {
        marginLeft: 12,
        flex: 1,
    },
    streakTitle: {
        fontSize: 18,
        fontWeight: '700',
        marginBottom: 4,
    },
    streakCount: {
        fontSize: 24,
        fontWeight: '800',
    },
    badgeContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingTop: 12,
        borderTopWidth: 1,
        borderTopColor: '#E5E5E5',
    },
    badgeText: {
        fontSize: 14,
        fontWeight: '600',
        marginLeft: 8,
    },

    // Result Item
    resultItem: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
        marginBottom: 12,
        borderRadius: 12,
        borderWidth: 1,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    leftSection: {
        flex: 1,
    },
    dateText: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 4,
    },
    timeText: {
        fontSize: 12,
        fontWeight: '500',
    },
    middleSection: {
        flex: 1,
        alignItems: 'center',
    },
    scoreContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 20,
        gap: 6,
    },
    scoreText: {
        fontSize: 16,
        fontWeight: '700',
    },
    rightSection: {
        flex: 1,
        alignItems: 'flex-end',
    },
    coinsBadge: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 16,
        gap: 6,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    uestCoinIcon: {
        width: 16,
        height: 16,
        borderRadius: 8,
        backgroundColor: '#FD904B',
        alignItems: 'center',
        justifyContent: 'center',
    },
    uestCoinText: {
        fontSize: 10,
        fontWeight: '800',
        color: '#FFFFFF',
    },
    coinsText: {
        fontSize: 14,
        fontWeight: '600',
    },

    // Loading & Empty States
    loadingContainer: {
        paddingVertical: 20,
        alignItems: 'center',
    },
    emptyContainer: {
        alignItems: 'center',
        paddingVertical: 60,
    },
    emptyText: {
        fontSize: 18,
        fontWeight: '600',
        marginTop: 16,
        marginBottom: 8,
    },
    emptySubtext: {
        fontSize: 14,
        fontWeight: '500',
        textAlign: 'center',
        paddingHorizontal: 40,
    },
});
