import React from 'react';
import {View, StyleSheet, Dimensions, Text, Image} from 'react-native';
import {SafeAreaProvider, SafeAreaView} from 'react-native-safe-area-context';
import {PrimaryColors} from '../../Utils/Constants';
import IndexStyle from '../../Theme/IndexStyle';
import { ScrollView } from 'react-native-gesture-handler';
const {width, height} = Dimensions.get('window');

const Profile = ({navigation}) => {
  const {styles, isDarkMode} = IndexStyle();
  return (
    <SafeAreaProvider style={{backgroundColor: PrimaryColors.BLACK}}>
      <SafeAreaView
        style={[Basestyles.container, {backgroundColor: PrimaryColors.BLACK}]}
        edges={['left', 'right']}>
        {/* Header */}
        <View style={Basestyles.header}>
          <View style={{}}>
            <Text style={Basestyles.dashboardTitle}>Profile</Text>
            <View style={Basestyles.userRow}>
              <View style={{marginRight: '4%'}}>
                <Image
                  source={{uri: 'https://i.pravatar.cc/200'}}
                  style={Basestyles.avatar}
                />
              </View>

              <View style={{marginLeft: '4%'}}>
                <Text style={[Basestyles.nameText, {paddingTop: 4}]}>
                  Saif Ali Khan
                </Text>

                <Text style={Basestyles.detailText}>
                  Class : L.K.G [ENGLISH]{' '}
                </Text>
                <View style={{flexDirection: 'row'}}>
                  <Text style={Basestyles.detailText}>Status : </Text>
                  <View style={Basestyles.statusBadge}>
                    <Text style={Basestyles.statusText}>
                      Active
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          </View>
        </View>

        {/* Grid */}
        <View
          style={[
            Basestyles.gridContainer,
            {backgroundColor: isDarkMode ? '#161616' : PrimaryColors.WHITE},
          ]}>
          <ScrollView style={{marginBottom:100}} showsVerticalScrollIndicator={false}>
            {/* Father Name */}
            <View style={[Basestyles.detailContainer,{borderBottomColor: isDarkMode?'#8B8B8B':'#CCCCCC',}]}>
              <Text style={Basestyles.labelText}>
                Father Name :
              </Text>
              <Text style={[Basestyles.valueText,{color:isDarkMode?'#CCCCCC':PrimaryColors.BLACK}]}>
                Mahendrabhai
              </Text>
            </View>

            {/* Gender */}
            <View style={[Basestyles.detailContainer,{borderBottomColor: isDarkMode?'#8B8B8B':'#CCCCCC',}]}>
              <Text style={Basestyles.labelText}>
                Gender :
              </Text>
              <Text style={[Basestyles.valueText,{color:isDarkMode?'#CCCCCC':PrimaryColors.BLACK}]}>Male</Text>
            </View>

            {/* Date of Birth */}
            <View style={[Basestyles.detailContainer,{borderBottomColor: isDarkMode?'#8B8B8B':'#CCCCCC',}]}>
              <Text style={Basestyles.labelText}>
                Date of Birth :
              </Text>
              <Text style={[Basestyles.valueText,{color:isDarkMode?'#CCCCCC':PrimaryColors.BLACK}]}>02/02/2002</Text>
            </View>

            {/* Contact No */}
            <View style={[Basestyles.detailContainer,{borderBottomColor: isDarkMode?'#8B8B8B':'#CCCCCC',}]}>
              <Text style={Basestyles.labelText}>
                Contact No :
              </Text>
              <Text style={[Basestyles.valueText,{color:isDarkMode?'#CCCCCC':PrimaryColors.BLACK}]}>78794 32332</Text>
            </View>

            {/* E-mail */}
            <View style={[Basestyles.detailContainer,{borderBottomColor: isDarkMode?'#8B8B8B':'#CCCCCC',}]}>
              <Text style={Basestyles.labelText}>
                E-mail :
              </Text>
              <Text style={[Basestyles.valueText,{color:isDarkMode?'#CCCCCC':PrimaryColors.BLACK}]}>
                <EMAIL>
              </Text>
            </View>

            {/* Address */}
            <View style={[Basestyles.detailContainer,{borderBottomColor: isDarkMode?'#8B8B8B':'#CCCCCC',}]}>
              <Text style={Basestyles.labelText}>
                Address :
              </Text>
              <Text style={[Basestyles.valueText,{color:isDarkMode?'#CCCCCC':PrimaryColors.BLACK}]}>
                Shree Ram park -2, mavdi area, Rajkot-360004
              </Text>
            </View>

            {/* Academic Info Section */}
            <View style={[Basestyles.infoCard,{borderColor:isDarkMode?'#8B8B8B':'#CCCCCC'}]}>
              <View style={{flexDirection:'row'}}>
                <Text style={[Basestyles.infoTitle,{color:isDarkMode?'#CCCCCC':PrimaryColors.BLACK}]}>Academic</Text>
                <Text style={Basestyles.infoTitleHighlight}>Info</Text>
              </View>
              <Text style={[Basestyles.infoText,{color:isDarkMode?'#CCCCCC':PrimaryColors.BLACK}]}>Department : Education</Text>
              <Text style={[Basestyles.infoText,{color:isDarkMode?'#CCCCCC':PrimaryColors.BLACK}]}>Year : 2025-2026</Text>
              <Text style={[Basestyles.infoText,{color:isDarkMode?'#CCCCCC':PrimaryColors.BLACK}]}>Class Teacher : Sham Rathod</Text>
              <Text style={[Basestyles.infoText,{color:isDarkMode?'#CCCCCC':PrimaryColors.BLACK}]}>Class Teacher Contact : 99999 99999</Text>
            </View>

            {/* Other Info Section */}
            <View style={[Basestyles.infoCard,{borderColor:isDarkMode?'#8B8B8B':'#CCCCCC'}]}>
              <View style={{flexDirection:'row'}}>
                <Text style={[Basestyles.infoTitle,{color:isDarkMode?'#CCCCCC':PrimaryColors.BLACK}]}>Other</Text>
                <Text style={Basestyles.infoTitleHighlight}>Info</Text>
              </View>
              <Text style={[Basestyles.infoText,{color:isDarkMode?'#CCCCCC':PrimaryColors.BLACK}]}>Mother Name : Bhavnaben</Text>
              <Text style={[Basestyles.infoText,{color:isDarkMode?'#CCCCCC':PrimaryColors.BLACK}]}>Contact no. : 99988 22356</Text>
            </View>
          </ScrollView>
        </View>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

const Basestyles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    height: height * 0.30,
    paddingTop: '15%',
    padding: 16,
  },
  dashboardTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 10,
    textAlign: 'center',
  },
  userRow: {
    flexDirection: 'row',
    marginTop: '5%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  userInfo: {
    flexDirection: 'row',
    flex: 1,
    marginRight: 10,
  },
  welcomeText: {
    color: '#FF914D',
    fontSize: 20,
  },
  nameText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
    paddingTop: 2,
  },
  detailText: {
    color: '#969696',
    fontSize: 14,
    marginTop: 4,
    marginBottom: 5,
    letterSpacing: 0.2,
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
  },
  gridContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 20,
    height: height * 0.70,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  statusBadge: {
    backgroundColor: '#DB7D42',
    marginLeft: 3,
    borderRadius: 10,
    height: 18,
    width: 83,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 6,
  },
  statusText: {
    color: PrimaryColors.WHITE,
    fontSize: 12,
    lineHeight: 16,
    textAlign: 'center',
  },
  detailContainer: {
    marginBottom: 12,
    borderBottomColor: '#CCCCCC',
    borderBottomWidth: 1,
    paddingBottom: 8,
  },
  labelText: {
    color: '#6E6E6E',
    fontSize: 16,
    fontWeight: 'bold',
  },
  valueText: {
    color: PrimaryColors.BLACK,
    fontSize: 16,
    paddingLeft: 6,
    paddingTop: 10,
    fontWeight: '400',
  },
  infoCard: {
    width: '100%',
    borderWidth: 1,
    borderRadius: 16,
    marginTop: 20,
    padding: 10,
    borderColor: '#CCCCCC',
  },
  infoTitle: {
    marginRight: 5,
    fontSize: 18,
    fontWeight: '400',
    color: PrimaryColors.BLACK,
  },
  infoTitleHighlight: {
    fontSize: 18,
    fontWeight: '500',
    color: '#FF914D',
  },
  infoText: {
    marginTop: 12,
    marginBottom: 4,
    fontSize: 14,
  },
});

export default Profile;