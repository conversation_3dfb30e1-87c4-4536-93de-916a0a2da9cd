/* eslint-disable react/no-unstable-nested-components */
import React from 'react';
import { View, StyleSheet, Platform } from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useSelector } from 'react-redux';
import { getFocusedRouteNameFromRoute } from '@react-navigation/native';

import { RootState } from '../Redux/store';
import Home from '../Screens/Student/Home/Home';
import StudentProfile from '../Screens/Student/StudentProfile/StudentProfile';
import Payment from '../Screens/Student/Payment/Payment';
import Setting from '../Screens/Student/Setting/Setting';
import strings from '../Utils/LocalizedStrings/LocalizedStrings';
import ClassList from '../Screens/Student/ClassList/ClassList';
import Exam from '../Screens/Student/Exam/Exam';
import ERPStack from './ERPStack';
import DailyQuiz from '../Screens/Student/DailyQuiz/DailyQuiz';

const Tab = createBottomTabNavigator();

const TabNavigator = () => {
  const isDarkMode = useSelector((state: RootState) => state.theme.isDarkMode);
  const styles = isDarkMode ? darkStyles : lightStyles;

  const tabItems = [
    { name: strings.Home.HOME, component: Home, icon: 'home' },
    { name: strings.Home.DAILYQUIZ, component: DailyQuiz, icon: 'bulb' },
    { name: strings.Home.SEARCH, component: ClassList, icon: 'search' },
    { name: strings.Home.SETTING, component: Setting, icon: 'settings' },
    // { name: 'My Class', component: ERPStack, icon: 'business' },
  ];

  return (
    <Tab.Navigator
      screenOptions={({ route }) => {
        const routeName = getFocusedRouteNameFromRoute(route) ?? '';

        const hideTabBar = route.name === 'My Class';

        const tabItem = tabItems.find((item) => item.name === route.name);
        const iconName = tabItem?.icon || 'circle';

        return {
          headerShown: false,
          tabBarStyle: hideTabBar ? { display: 'none' } : styles.tabBar,
          tabBarLabelStyle: styles.tabLabel,
          tabBarActiveTintColor: isDarkMode ? '#FFFFFF' : '#000000',
          tabBarInactiveTintColor: '#DBCDCD',
          tabBarIcon: ({ focused, color, size }) => (
            <>
              {focused && <View style={styles.headercolor} />}
              <Ionicons
                name={focused ? iconName : `${iconName}-outline`}
                size={size}
                color={color}
              />
            </>
          ),
        };
      }}
    >
      {tabItems.map((item, index) => (
        <Tab.Screen
          key={index}
          name={item.name}
          component={item.component}
          options={{ tabBarLabel: item.name }}
        />
      ))}
    </Tab.Navigator>
  );
};

export default TabNavigator;

// Light mode styles
const lightStyles = StyleSheet.create({
  tabBar: {
    backgroundColor: '#ffffff',
    height: Platform.OS === 'android' ? 60 : 80,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  tabLabel: {
    fontSize: 12,
    marginTop: 10,
  },
  headercolor: {
    borderWidth: 1,
    width: '40%',
    marginBottom: 8,
    marginTop: 8,
    height: 1,
    backgroundColor: '#000000',
  },
});

// Dark mode styles
const darkStyles = StyleSheet.create({
  tabBar: {
    backgroundColor: '#000000',
    height: Platform.OS === 'android' ? 60 : 80,
    shadowColor: '#fff',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  tabLabel: {
    fontSize: 12,
    marginTop: 10,
  },
  headercolor: {
    borderWidth: 1,
    width: '40%',
    marginBottom: 8,
    marginTop: 8,
    height: 1,
    backgroundColor: '#FFFFFF',
  },
});
